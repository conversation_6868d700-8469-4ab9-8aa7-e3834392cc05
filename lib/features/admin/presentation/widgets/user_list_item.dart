import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../user/domain/entities/user_model.dart';

class UserListItem extends StatelessWidget {
  final UserModel user;
  final VoidCallback? onTap;
  final VoidCallback? onTogglePremium;
  final VoidCallback? onDelete;

  const UserListItem({
    super.key,
    required this.user,
    this.onTap,
    this.onTogglePremium,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              _buildAvatar(),
              const SizedBox(width: 16),
              Expanded(child: _buildUserInfo()),
              _buildActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return Stack(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: AppTheme.lightGray,
          backgroundImage: user.photoUrl != null
              ? CachedNetworkImageProvider(user.photoUrl!)
              : null,
          child: user.photoUrl == null
              ? Icon(
                  Icons.person,
                  color: AppTheme.secondaryText,
                  size: 24,
                )
              : null,
        ),
        if (user.isPremium)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: AppTheme.premiumGold,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1),
              ),
              child: Icon(
                Icons.star,
                color: Colors.white,
                size: 12,
              ),
            ),
          ),
        if (user.isAdmin)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: AppTheme.errorRed,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1),
              ),
              child: Icon(
                Icons.admin_panel_settings,
                color: Colors.white,
                size: 10,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                user.name,
                style: AppTheme.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            _buildUserTypeChip(),
          ],
        ),
        const SizedBox(height: 4),
        if (user.email != null)
          Text(
            user.email!,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.secondaryText,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        if (user.phoneNumber != null)
          Text(
            user.phoneNumber!,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.secondaryText,
            ),
          ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(
              Icons.access_time,
              size: 12,
              color: AppTheme.secondaryText,
            ),
            const SizedBox(width: 4),
            Text(
              'Joined ${_formatDate(user.createdAt)}',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.secondaryText,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUserTypeChip() {
    final userType = user.userType ?? 'user';
    Color chipColor;
    IconData chipIcon;

    switch (userType) {
      case 'businessman':
        chipColor = AppTheme.primaryBlue;
        chipIcon = Icons.business;
        break;
      case 'politician':
        chipColor = AppTheme.errorRed;
        chipIcon = Icons.account_balance;
        break;
      default:
        chipColor = AppTheme.successGreen;
        chipIcon = Icons.person;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            chipIcon,
            size: 12,
            color: chipColor,
          ),
          const SizedBox(width: 4),
          Text(
            _formatUserType(userType),
            style: AppTheme.bodySmall.copyWith(
              color: chipColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: AppTheme.secondaryText,
      ),
      onSelected: (value) {
        switch (value) {
          case 'toggle_premium':
            onTogglePremium?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'toggle_premium',
          child: Row(
            children: [
              Icon(
                user.isPremium ? Icons.star_border : Icons.star,
                color: AppTheme.premiumGold,
              ),
              const SizedBox(width: 8),
              Text(user.isPremium ? 'Remove Premium' : 'Make Premium'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, color: Colors.red),
              SizedBox(width: 8),
              Text('Delete User', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  String _formatUserType(String userType) {
    switch (userType) {
      case 'businessman':
        return 'Business';
      case 'politician':
        return 'Political';
      default:
        return 'Regular';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }
}
