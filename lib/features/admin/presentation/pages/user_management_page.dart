import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../domain/usecases/admin_service.dart';
import '../widgets/user_list_item.dart';

class UserManagementPage extends StatefulWidget {
  const UserManagementPage({super.key});

  @override
  State<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends State<UserManagementPage> {
  List<UserModel> _users = [];
  bool _isLoading = true;
  String? _error;
  String? _selectedUserType;
  bool? _selectedPremiumStatus;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _hasMoreUsers = true;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _loadUsers();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreUsers();
    }
  }

  Future<void> _loadUsers({bool refresh = false}) async {
    try {
      setState(() {
        if (refresh) {
          _users.clear();
          _hasMoreUsers = true;
        }
        _isLoading = refresh || _users.isEmpty;
        _error = null;
      });

      final adminService = Provider.of<AdminService>(context, listen: false);
      final users = await adminService.getAllUsers(
        limit: 20,
        searchQuery: _searchController.text.isNotEmpty ? _searchController.text : null,
        userType: _selectedUserType,
        isPremium: _selectedPremiumStatus,
      );

      setState(() {
        if (refresh || _users.isEmpty) {
          _users = users;
        } else {
          _users.addAll(users);
        }
        _hasMoreUsers = users.length == 20;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreUsers() async {
    if (_isLoadingMore || !_hasMoreUsers) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      final adminService = Provider.of<AdminService>(context, listen: false);
      final users = await adminService.getAllUsers(
        limit: 20,
        lastUserId: _users.isNotEmpty ? _users.last.uid : null,
        searchQuery: _searchController.text.isNotEmpty ? _searchController.text : null,
        userType: _selectedUserType,
        isPremium: _selectedPremiumStatus,
      );

      setState(() {
        _users.addAll(users);
        _hasMoreUsers = users.length == 20;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading more users: $e')),
        );
      }
    }
  }

  void _onSearchChanged() {
    // Debounce search
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == _searchController.text) {
        _loadUsers(refresh: true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'User Management',
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: isPremium ? AppTheme.premiumGold : Colors.white,
            ),
            onPressed: () => _loadUsers(refresh: true),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: Column(
          children: [
            _buildSearchAndFilters(),
            Expanded(child: _buildUsersList()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            onChanged: (_) => _onSearchChanged(),
            decoration: InputDecoration(
              hintText: 'Search users by name, email, or phone...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          // Filters
          LayoutBuilder(
            builder: (context, constraints) {
              // Use column layout on smaller screens
              if (constraints.maxWidth < 600) {
                return Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: _selectedUserType,
                      decoration: InputDecoration(
                        labelText: 'User Type',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('All Types')),
                        DropdownMenuItem(value: 'user', child: Text('Regular')),
                        DropdownMenuItem(value: 'businessman', child: Text('Business')),
                        DropdownMenuItem(value: 'politician', child: Text('Politician')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedUserType = value;
                        });
                        _loadUsers(refresh: true);
                      },
                    ),
                    const SizedBox(height: 12),
                    DropdownButtonFormField<bool>(
                      value: _selectedPremiumStatus,
                      decoration: InputDecoration(
                        labelText: 'Premium Status',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('All Users')),
                        DropdownMenuItem(value: true, child: Text('Premium')),
                        DropdownMenuItem(value: false, child: Text('Free')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedPremiumStatus = value;
                        });
                        _loadUsers(refresh: true);
                      },
                    ),
                  ],
                );
              } else {
                // Use row layout on larger screens
                return Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedUserType,
                        decoration: InputDecoration(
                          labelText: 'User Type',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('All Types')),
                          DropdownMenuItem(value: 'user', child: Text('Regular')),
                          DropdownMenuItem(value: 'businessman', child: Text('Business')),
                          DropdownMenuItem(value: 'politician', child: Text('Politician')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedUserType = value;
                          });
                          _loadUsers(refresh: true);
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<bool>(
                        value: _selectedPremiumStatus,
                        decoration: InputDecoration(
                          labelText: 'Premium Status',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('All Users')),
                          DropdownMenuItem(value: true, child: Text('Premium')),
                          DropdownMenuItem(value: false, child: Text('Free')),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedPremiumStatus = value;
                          });
                          _loadUsers(refresh: true);
                        },
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList() {
    if (_isLoading && _users.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && _users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppTheme.errorRed),
            const SizedBox(height: 16),
            Text('Error loading users', style: AppTheme.headingMedium),
            const SizedBox(height: 8),
            Text(_error!, style: AppTheme.bodyMedium),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _loadUsers(refresh: true),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: AppTheme.secondaryText),
            const SizedBox(height: 16),
            Text('No users found', style: AppTheme.headingMedium),
            const SizedBox(height: 8),
            Text('Try adjusting your search or filters', style: AppTheme.bodyMedium),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadUsers(refresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _users.length + (_hasMoreUsers ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _users.length) {
            return _isLoadingMore
                ? const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(child: CircularProgressIndicator()),
                  )
                : const SizedBox.shrink();
          }

          return UserListItem(
            user: _users[index],
            onTap: () => _showUserDetails(_users[index]),
            onTogglePremium: () => _toggleUserPremium(_users[index]),
            onDelete: () => _deleteUser(_users[index]),
          );
        },
      ),
    );
  }

  void _showUserDetails(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('User Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Name', user.name),
              _buildDetailRow('Email', user.email ?? 'Not provided'),
              _buildDetailRow('Phone', user.phoneNumber ?? 'Not provided'),
              _buildDetailRow('User Type', user.userType ?? 'Regular'),
              _buildDetailRow('Premium', user.isPremium ? 'Yes' : 'No'),
              _buildDetailRow('Admin', user.isAdmin ? 'Yes' : 'No'),
              _buildDetailRow('Profile Complete', user.isProfileComplete ? 'Yes' : 'No'),
              _buildDetailRow('Created', user.createdAt.toString().split('.')[0]),
              _buildDetailRow('Last Updated', user.updatedAt.toString().split('.')[0]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value, style: AppTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleUserPremium(UserModel user) async {
    try {
      final adminService = Provider.of<AdminService>(context, listen: false);
      await adminService.toggleUserPremium(user.uid, !user.isPremium);

      setState(() {
        final index = _users.indexWhere((u) => u.uid == user.uid);
        if (index != -1) {
          _users[index] = user.copyWith(isPremium: !user.isPremium);
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              user.isPremium
                  ? 'User premium status removed'
                  : 'User upgraded to premium',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating user: $e')),
        );
      }
    }
  }

  Future<void> _deleteUser(UserModel user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.name}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final adminService = Provider.of<AdminService>(context, listen: false);
        await adminService.deleteUser(user.uid);

        if (mounted) {
          setState(() {
            _users.removeWhere((u) => u.uid == user.uid);
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('User deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting user: $e')),
          );
        }
      }
    }
  }
}
