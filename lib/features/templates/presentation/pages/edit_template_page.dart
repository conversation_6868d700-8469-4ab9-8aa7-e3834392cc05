import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:image_picker/image_picker.dart';
import 'package:screenshot/screenshot.dart';
import 'dart:io';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../../../core/providers/template_customization_provider.dart';
import '../../../../core/providers/template_image_customization_provider.dart';
import '../../../user/domain/entities/user_model.dart';
import '../../../user/domain/entities/party.dart';
import '../../../user/domain/usecases/user_service.dart';
import '../../../user/domain/usecases/party_service.dart';
import '../../../banners/domain/entities/banner_item.dart';
import '../../../banners/domain/usecases/banner_service.dart';
import '../../domain/entities/template_item.dart';
import '../widgets/template_card_components/party_components.dart';
import '../widgets/template_card_components/user_info_components.dart';
import '../widgets/template_card_components/additional_image_handler.dart';
import '../widgets/template_card_components/sharing_utilities.dart';
import '../widgets/template_card_components/action_buttons.dart';
class HexagonClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    path.moveTo(width * 0.25, 0);
    path.lineTo(width * 0.75, 0);
    path.lineTo(width, height * 0.5);
    path.lineTo(width * 0.75, height);
    path.lineTo(width * 0.25, height);
    path.lineTo(0, height * 0.5);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

class EditTemplatePage extends StatefulWidget {
  final TemplateItem template;
  final BannerItem? initialBanner;
  final String? initialImageShape;
  final String? initialImageSource;
  final String? initialCustomImageUrl;
  final List<Map<String, dynamic>>? initialAdditionalImages;
  final Offset? initialProfileImageOffset;
  final double? initialProfileImageScale;
  
  // Banner text customization parameters
  final String? initialBannerNameDisplayOption;
  final String? initialBannerContactDisplayOption;
  final Color? initialBannerNameTextColor;
  final String? initialBannerNameTextCase;
  final Color? initialBannerContactTextColor;
  final String? initialBannerContactTextCase;

  const EditTemplatePage({
    super.key,
    required this.template,
    this.initialBanner,
    this.initialImageShape,
    this.initialImageSource,
    this.initialCustomImageUrl,
    this.initialAdditionalImages,
    this.initialProfileImageOffset,
    this.initialProfileImageScale,
    this.initialBannerNameDisplayOption,
    this.initialBannerContactDisplayOption,
    this.initialBannerNameTextColor,
    this.initialBannerNameTextCase,
    this.initialBannerContactTextColor,
    this.initialBannerContactTextCase,
  });

  @override
  State<EditTemplatePage> createState() => _EditTemplatePageState();
}

class _EditTemplatePageState extends State<EditTemplatePage> {
  final ScreenshotController _screenshotController = ScreenshotController();

  UserModel? _user;
  Party? _selectedParty;
  BannerItem? _selectedBanner;
  List<BannerItem> _banners = [];
  bool _isLoadingUser = true;
  bool _isLoadingBanners = false;
  bool _isLoadingMoreBanners = false;
  List<PartyLeader> _partyLeadership = [];
  final ScrollController _bannerScrollController = ScrollController();

  // Image shape options
  String _selectedImageShape = 'none'; // Options: 'none', 'circle', 'rectangle', 'rounded_rectangle', 'diamond', 'hexagon'

  // Image source selection
  String _selectedImageSource = 'auto'; // Options: 'auto', 'business', 'profile', 'political', 'custom'
  String? _customImageUrl;

  // Additional images support
  List<Map<String, dynamic>> _additionalImages = [];
  final ImagePicker _imagePicker = ImagePicker();

  // Profile image positioning and scaling
  Offset _profileImageOffset = Offset.zero;
  double _profileImageScale = 1.0;

  @override
  void initState() {
    super.initState();
    _selectedBanner = widget.initialBanner;
    
    // Initialize with provided values or defaults
    _selectedImageShape = widget.initialImageShape ?? 'none';
    _selectedImageSource = widget.initialImageSource ?? 'auto';
    _customImageUrl = widget.initialCustomImageUrl;
    _additionalImages = widget.initialAdditionalImages != null 
        ? List<Map<String, dynamic>>.from(widget.initialAdditionalImages!)
        : [];
    _profileImageOffset = widget.initialProfileImageOffset ?? Offset.zero;
    _profileImageScale = widget.initialProfileImageScale ?? 1.0;
    
    _loadUserData();
    _loadInitialBanners();
    _bannerScrollController.addListener(_onBannerScroll);
    _initializeCustomizations();
  }

  void _initializeCustomizations() {
    final imageCustomization = Provider.of<TemplateImageCustomizationProvider>(context, listen: false);
    final textCustomization = Provider.of<TemplateCustomizationProvider>(context, listen: false);

    // Initialize image customizations
    setState(() {
      _profileImageOffset = imageCustomization.mainImageCustomization.offset;
      _profileImageScale = imageCustomization.mainImageCustomization.scale;
      _selectedImageShape = imageCustomization.mainImageCustomization.shape;
      _selectedImageSource = imageCustomization.mainImageCustomization.source;
      _customImageUrl = imageCustomization.mainImageCustomization.customUrl;

      // Initialize additional images
      _additionalImages = imageCustomization.additionalImages.entries.map((entry) {
        return {
          'offset': entry.value.offset,
          'scale': entry.value.scale,
          'url': entry.value.imageUrl,
          'source': 'url',
          'shape': 'circle', // Default shape
        };
      }).toList();
    });
  }

  @override
  void dispose() {
    _bannerScrollController.removeListener(_onBannerScroll);
    _bannerScrollController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final userService = Provider.of<UserService>(context, listen: false);
      final user = await userService.getCurrentUser();

      if (user != null && user.userType == 'politician') {
        if (!mounted) return;
        final partyService = Provider.of<PartyService>(context, listen: false);
        final parties = await partyService.getAllParties();

        // Find the user's selected party based on party_name parameter
        if (user.politicalProfile?.parameterValues != null) {
          final partyName = user.politicalProfile!.parameterValues!['party_name'];
          if (partyName != null) {
            _selectedParty = parties.firstWhere(
              (party) => party.id == partyName || party.name == partyName,
              orElse: () => parties.isNotEmpty ? parties.first : parties.first,
            );

            // Load party leadership
            if (_selectedParty != null) {
              _partyLeadership = _selectedParty!.leadership;
            }
          }
        }
      }

      if (mounted) {
        setState(() {
          _user = user;
          _isLoadingUser = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingUser = false;
        });
      }
    }
  }

  Future<void> _loadInitialBanners() async {
    if (_isLoadingBanners) return;

    setState(() {
      _isLoadingBanners = true;
    });

    try {
      final bannerService = Provider.of<BannerService>(context, listen: false);
      bannerService.resetPagination(); // Reset pagination (but keep cache)
      final banners = await bannerService.getBanners(limit: 5);

      if (mounted) {
        setState(() {
          _banners = banners;
          _isLoadingBanners = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingBanners = false;
        });
      }
    }
  }

  Future<void> _loadMoreBanners() async {
    final bannerService = Provider.of<BannerService>(context, listen: false);

    if (_isLoadingMoreBanners || !bannerService.hasMoreBanners) return;

    setState(() {
      _isLoadingMoreBanners = true;
    });

    try {
      final moreBanners = await bannerService.getBanners(limit: 5);

      if (mounted && moreBanners.isNotEmpty) {
        setState(() {
          _banners.addAll(moreBanners);
          _isLoadingMoreBanners = false;
        });
      } else if (mounted) {
        setState(() {
          _isLoadingMoreBanners = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading more banners: $e');
      if (mounted) {
        setState(() {
          _isLoadingMoreBanners = false;
        });
      }
    }
  }

  void _onBannerScroll() {
    if (_bannerScrollController.hasClients &&
        _bannerScrollController.position.pixels >=
        _bannerScrollController.position.maxScrollExtent - 100) {
      _loadMoreBanners();
    }
  }

  void _selectBanner(BannerItem? banner) {
    setState(() {
      _selectedBanner = banner;
    });
  }

  String? _getProfilePhotoUrl() {
    if (_user == null) {
      return null;
    }

    // Handle custom image selection
    if (_selectedImageSource == 'custom' && _customImageUrl != null) {
      return _customImageUrl;
    }

    // Handle specific image source selections
    switch (_selectedImageSource) {
      case 'business':
        if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
          // Business user - prioritize business logo
          if (_user!.businessProfile!.logoUrl != null && _user!.businessProfile!.logoUrl!.isNotEmpty) {
            return _user!.businessProfile!.logoUrl;
          }
          // Check parameter values for logo
          if (_user!.businessProfile!.parameterValues != null) {
            for (var entry in _user!.businessProfile!.parameterValues!.entries) {
              if (entry.key.toLowerCase().contains('logo') &&
                  entry.value is String &&
                  (entry.value as String).isNotEmpty) {
                return entry.value as String;
              }
            }
          }
        }
        break;

      case 'profile':
        // Use user's profile photo
        if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
          return _user!.photoUrl;
        }
        break;

      case 'political':
        if (_user!.userType == 'politician' && _user!.politicalProfile != null) {
          final paramValues = _user!.politicalProfile!.parameterValues;
          if (paramValues != null) {
            final photoUrl = paramValues['political_photo'] as String?;
            if (photoUrl != null && photoUrl.isNotEmpty) {
              return photoUrl;
            }
          }
        }
        break;

      case 'auto':
      default:
        // Auto selection based on user type (original logic)
        if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
          // Business user - prioritize business logo
          if (_user!.businessProfile!.logoUrl != null && _user!.businessProfile!.logoUrl!.isNotEmpty) {
            return _user!.businessProfile!.logoUrl;
          }
          // Check parameter values for logo
          if (_user!.businessProfile!.parameterValues != null) {
            for (var entry in _user!.businessProfile!.parameterValues!.entries) {
              if (entry.key.toLowerCase().contains('logo') &&
                  entry.value is String &&
                  (entry.value as String).isNotEmpty) {
                return entry.value as String;
              }
            }
          }
          // Fallback to user photo
          if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
            return _user!.photoUrl;
          }
        } else if (_user!.userType == 'politician' && _user!.politicalProfile != null) {
          // Political user - prioritize political photo
          final paramValues = _user!.politicalProfile!.parameterValues;
          if (paramValues != null) {
            final photoUrl = paramValues['political_photo'] as String?;
            if (photoUrl != null && photoUrl.isNotEmpty) {
              return photoUrl;
            }
          }
          // Fallback to user photo
          if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
            return _user!.photoUrl;
          }
        } else {
          // Regular user - use profile photo
          if (_user!.photoUrl != null && _user!.photoUrl!.isNotEmpty) {
            return _user!.photoUrl;
          }
        }
        break;
    }

    return null;
  }

  void _constrainProfileImageOffset() {
    final screenSize = MediaQuery.of(context).size;
    final templateWidth = screenSize.width - 32; // Account for padding
    final templateHeight = screenSize.height * 0.6;
    final imageSize = 64.0;

    // Calculate boundaries
    final leftBoundary = 0.0;
    final rightBoundary = templateWidth - imageSize;
    final topBoundary = 0.0;
    final bottomBoundary = templateHeight - imageSize;

    // If banner exists, adjust the bottom boundary
    final bannerHeight = _selectedBanner != null ? 80.0 : 0.0;
    final absoluteBottomBoundary = bottomBoundary + bannerHeight;

    // Calculate initial position (bottom-right with padding)
    final initialX = templateWidth - imageSize - 40;
    final initialY = templateHeight - imageSize - 40;

    // Get absolute position
    final absoluteX = initialX + _profileImageOffset.dx;
    final absoluteY = initialY + _profileImageOffset.dy;

    // Constrain position
    final constrainedX = absoluteX.clamp(leftBoundary, rightBoundary);
    final constrainedY = absoluteY.clamp(topBoundary, absoluteBottomBoundary);

    // Update offset
    _profileImageOffset = Offset(
      constrainedX - initialX,
      constrainedY - initialY,
    );
  }

  Widget? _buildTopBanner(bool isPremium) {
    if (_user == null) return null;

    String? displayName;

    // Only show top banner for business users
    if (_user!.userType == 'businessman' && _user!.businessProfile != null) {
      displayName = _user!.businessProfile!.businessName;

      if ((displayName == null || displayName.isEmpty) &&
          _user!.businessProfile!.parameterValues != null) {
        final businessNameParam = _user!.businessProfile!.parameterValues!['business_name'] ??
                                 _user!.businessProfile!.parameterValues!['company_name'] ??
                                 _user!.businessProfile!.parameterValues!['organization_name'];

        if (businessNameParam != null && businessNameParam is String && businessNameParam.isNotEmpty) {
          displayName = businessNameParam;
        }
      }
    }

    if (displayName == null || displayName.isEmpty) return null;

    return PartyComponents.buildTopBanner(displayName.toUpperCase(), isPremium);
  }

  Widget _buildTemplatePreview(bool isPremium) {
    final screenSize = MediaQuery.of(context).size;
    final templateWidth = screenSize.width - 32;
    final templateHeight = screenSize.height * 0.6;
    final String? photoUrl = _getProfilePhotoUrl();

    return Screenshot(
      controller: _screenshotController,
      child: Container(
        width: templateWidth,
        height: templateHeight,
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // Template image
              Positioned.fill(
                child: CachedNetworkImage(
                  imageUrl: widget.template.imageUrl,
                  fit: BoxFit.fill,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[200],
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),

              // Top banner for business name
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: _buildTopBanner(isPremium) ?? const SizedBox.shrink(),
              ),

              // Political party logo (top right)
              if (_user?.userType == 'politician' && _selectedParty != null)
                Positioned(
                  top: 16,
                  right: 16,
                  child: PartyComponents.buildPartyLogo(_selectedParty!.logo) ?? const SizedBox.shrink(),
                ),

              // Leadership images on the left side in a row (if user is a politician)
              if (_user?.userType == 'politician' && _partyLeadership.isNotEmpty)
                Positioned(
                  top: 16,
                  left: 16,
                  child: PartyComponents.buildLeadershipImages(_partyLeadership) ?? const SizedBox.shrink(),
                ),

              // Banner at bottom with user info overlay (always show for user info)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Stack(
                  children: [
                    // Banner image or transparent background
                    Container(
                      height: 80,
                      width: double.infinity,
                      child: _selectedBanner != null
                          ? CachedNetworkImage(
                              imageUrl: _selectedBanner!.imageUrl,
                              height: 80,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                height: 80,
                                color: Colors.grey[200],
                                child: const Center(child: CircularProgressIndicator()),
                              ),
                              errorWidget: (context, url, error) => Container(
                                height: 80,
                                color: Colors.grey[200],
                                child: const Icon(Icons.error),
                              ),
                            )
                          : Container(
                              height: 80,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.transparent, // Fully transparent background
                                border: Border(
                                  top: BorderSide(
                                    color: isPremium ? AppTheme.premiumGold.withAlpha(100) : Colors.white.withAlpha(100),
                                    width: 1,
                                  ),
                                ),
                              ),
                            ),
                    ),

                    // User information overlay (interactive for editing)
                    UserInfoComponents.buildUserInfoOverlay(
                      _user, 
                      isPremium, 
                      isForSharing: false,
                    ),
                  ],
                ),
              ),

              // Business logo or political image with shape
              if (photoUrl != null)
                Positioned(
                  left: templateWidth - 64 - 40 + _profileImageOffset.dx,
                  top: templateHeight - 64 - 40 + _profileImageOffset.dy,
                  child: GestureDetector(
                    onScaleStart: (ScaleStartDetails details) {
                      setState(() {
                        // Handle scale start
                      });
                    },
                    onScaleUpdate: (ScaleUpdateDetails details) {
                      setState(() {
                        // Handle position change (dragging)
                        if (details.pointerCount == 1) {
                          _profileImageOffset += details.focalPointDelta;
                          _constrainProfileImageOffset();
                        }
                        // Handle scale change (pinch)
                        if (details.scale != 1.0) {
                          _profileImageScale = (_profileImageScale * details.scale).clamp(0.5, 2.0);
                        }
                      });
                    },
                    child: Transform.scale(
                      scale: _profileImageScale,
                      child: _buildShapedImage(photoUrl, 64, isPremium),
                    ),
                  ),
                ),

              // Additional images
              ..._additionalImages.asMap().entries.map((entry) {
                final index = entry.key;
                final image = entry.value;
                final offset = image['offset'] as Offset;
                final scale = image['scale'] as double;
                final shape = image['shape'] as String;
                
                // Position additional images in different locations to avoid overlap
                final baseX = 20.0 + (index * 80.0);
                final baseY = 20.0 + (index * 20.0);
                
                return Positioned(
                  left: baseX + offset.dx,
                  top: baseY + offset.dy,
                  child: AdditionalImageHandler(
                    imageData: image,
                    isPremium: isPremium,
                    initialOffset: offset,
                    initialScale: scale,
                    imageShape: shape,
                    imageSize: 64.0,
                    onScaleChanged: (newScale) {
                      setState(() {
                        _additionalImages[index]['scale'] = newScale;
                      });
                    },
                    onOffsetChanged: (newOffset) {
                      setState(() {
                        final constrainedOffset = _constrainAdditionalImageOffset(
                          newOffset, 
                          baseX, 
                          baseY, 
                          templateWidth, 
                          templateHeight
                        );
                        _additionalImages[index]['offset'] = constrainedOffset;
                      });
                    },
                    onShapeSelectionRequested: () => _showShapeSelectionForImage(index),
                  ),
                );
              }).toList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSourceSelection(bool isPremium) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Select Image Source',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ),

        // Horizontal image source list
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _getImageSourceOptions().length,
            itemBuilder: (context, index) {
              final source = _getImageSourceOptions()[index];
              return _buildImageSourceItem(source, isPremium);
            },
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getImageSourceOptions() {
    List<Map<String, dynamic>> options = [
      {'id': 'auto', 'name': 'Auto', 'icon': Icons.auto_awesome, 'description': 'Smart selection'},
    ];

    // Add business option if user is a businessman
    if (_user?.userType == 'businessman') {
      options.add({
        'id': 'business', 
        'name': 'Business', 
        'icon': Icons.business, 
        'description': 'Business logo'
      });
    }

    // Add political option if user is a politician
    if (_user?.userType == 'politician') {
      options.add({
        'id': 'political', 
        'name': 'Political', 
        'icon': Icons.account_balance, 
        'description': 'Political photo'
      });
    }

    // Always add profile and custom options
    options.addAll([
      {'id': 'profile', 'name': 'Profile', 'icon': Icons.person, 'description': 'Profile photo'},
      {'id': 'custom', 'name': 'Custom', 'icon': Icons.photo_library, 'description': 'From gallery'},
    ]);

    return options;
  }

  Widget _buildImageSourceItem(Map<String, dynamic> source, bool isPremium) {
    final bool isSelected = _selectedImageSource == source['id'];

    return GestureDetector(
      onTap: () async {
        if (source['id'] == 'custom') {
          await _selectCustomImage();
        } else {
          setState(() {
            _selectedImageSource = source['id'];
            _customImageUrl = null; // Clear custom image when switching to other sources
          });
        }
      },
      child: Container(
        width: 90,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                : Colors.grey.withAlpha(100),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Source icon
            Icon(
              source['icon'],
              size: 32,
              color: isSelected
                  ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                  : Colors.grey[600],
            ),

            const SizedBox(height: 4),

            // Source name
            Text(
              source['name'],
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected
                    ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                    : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),

            // Source description
            Text(
              source['description'],
              style: TextStyle(
                fontSize: 9,
                color: isSelected
                    ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                    : Colors.grey[500],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectCustomImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImageSource = 'custom';
          _customImageUrl = image.path; // Use local file path
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Custom image selected!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error selecting image: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Widget _buildImageShapeSelection(bool isPremium) {
    // Only show if user has business logo or political image
    if (_user == null) return const SizedBox.shrink();

    bool hasBusinessLogo = _user!.userType == 'businessman' &&
                          _user!.businessProfile != null &&
                          (_user!.businessProfile!.logoUrl != null ||
                           _getBusinessLogoFromParams() != null);

    bool hasPoliticalImage = _user!.userType == 'politician' &&
                            _user!.politicalProfile != null &&
                            _user!.politicalProfile!.parameterValues != null &&
                            _user!.politicalProfile!.parameterValues!['political_photo'] != null;

    if (!hasBusinessLogo && !hasPoliticalImage) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            _user!.userType == 'businessman' ? 'Business Logo Shape' : 'Image Shape',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ),

        // Horizontal shape list
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _getShapeOptions().length,
            itemBuilder: (context, index) {
              final shape = _getShapeOptions()[index];
              return _buildShapeItem(shape, isPremium);
            },
          ),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getShapeOptions() {
    return [
      {'id': 'none', 'name': 'No Shape', 'icon': Icons.crop_original},
      {'id': 'circle', 'name': 'Circle', 'icon': Icons.circle_outlined},
      {'id': 'rectangle', 'name': 'Rectangle', 'icon': Icons.rectangle_outlined},
      {'id': 'rounded_rectangle', 'name': 'Rounded', 'icon': Icons.rounded_corner},
      {'id': 'diamond', 'name': 'Diamond', 'icon': Icons.diamond_outlined},
      {'id': 'hexagon', 'name': 'Hexagon', 'icon': Icons.hexagon_outlined},
    ];
  }

  Widget _buildShapeItem(Map<String, dynamic> shape, bool isPremium) {
    final bool isSelected = _selectedImageShape == shape['id'];

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedImageShape = shape['id'];
        });
      },
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                : Colors.grey.withAlpha(100),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Shape icon
            Icon(
              shape['icon'],
              size: 32,
              color: isSelected
                  ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                  : Colors.grey[600],
            ),

            const SizedBox(height: 4),

            // Shape name
            Text(
              shape['name'],
              style: TextStyle(
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected
                    ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                    : Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  String? _getBusinessLogoFromParams() {
    if (_user?.businessProfile?.parameterValues != null) {
      for (var entry in _user!.businessProfile!.parameterValues!.entries) {
        if (entry.key.toLowerCase().contains('logo') &&
            entry.value is String &&
            (entry.value as String).isNotEmpty) {
          return entry.value as String;
        }
      }
    }
    return null;
  }

  Widget _buildShapedImage(String imageUrl, double size, bool isPremium) {
    Widget imageWidget = CachedNetworkImage(
      imageUrl: imageUrl,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        color: Colors.grey[200],
        child: const Center(child: CircularProgressIndicator()),
      ),
      errorWidget: (context, url, error) => Container(
        color: Colors.grey[200],
        child: const Icon(Icons.error),
      ),
    );

    switch (_selectedImageShape) {
      case 'circle':
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isPremium ? AppTheme.premiumGold : Colors.white,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: ClipOval(child: imageWidget),
        );

      case 'rectangle':
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            border: Border.all(
              color: isPremium ? AppTheme.premiumGold : Colors.white,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: ClipRect(child: imageWidget),
        );

      case 'rounded_rectangle':
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isPremium ? AppTheme.premiumGold : Colors.white,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: imageWidget,
          ),
        );

      case 'diamond':
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Transform.rotate(
            angle: 0.785398, // 45 degrees in radians
            child: Container(
              width: size * 0.7,
              height: size * 0.7,
              decoration: BoxDecoration(
                border: Border.all(
                  color: isPremium ? AppTheme.premiumGold : Colors.white,
                  width: 2,
                ),
              ),
              child: ClipRect(child: imageWidget),
            ),
          ),
        );

      case 'hexagon':
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: ClipPath(
            clipper: HexagonClipper(),
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: isPremium ? AppTheme.premiumGold : Colors.white,
                  width: 2,
                ),
              ),
              child: imageWidget,
            ),
          ),
        );

      case 'none':
      default:
        return Container(
          width: size,
          height: size,
          child: imageWidget,
        );
    }
  }

  Widget _buildDoneButton(bool isPremium) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: double.infinity,
        height: 56, // Slightly larger for the primary action
        decoration: BoxDecoration(
          gradient: isPremium 
              ? LinearGradient(
                  colors: [AppTheme.premiumGold, AppTheme.premiumGold.withAlpha(180)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [AppTheme.primaryBlue, AppTheme.primaryBlue.withAlpha(180)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue).withAlpha(150),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _onSave,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'Done',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      letterSpacing: 1.2,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onSave() async {
    try {
      // Get the providers
      final imageCustomization = Provider.of<TemplateImageCustomizationProvider>(context, listen: false);
      final textCustomization = Provider.of<TemplateCustomizationProvider>(context, listen: false);

      // Update image customizations
      imageCustomization.updateMainImageOffset(_profileImageOffset);
      imageCustomization.updateMainImageScale(_profileImageScale);
      imageCustomization.updateMainImageShape(_selectedImageShape);
      imageCustomization.updateMainImageSource(_selectedImageSource, customUrl: _customImageUrl);

      // Update additional images
      imageCustomization.clearAdditionalImages();
      for (var i = 0; i < _additionalImages.length; i++) {
        final image = _additionalImages[i];
        imageCustomization.updateAdditionalImage(
          i.toString(),
          offset: image['offset'] as Offset,
          scale: image['scale'] as double,
          imageUrl: image['url'] as String?,
        );
      }

      // Navigate back
      if (mounted) {
        Navigator.pop(context, {
          'success': true,
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save changes: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Edit Template',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _profileImageOffset = Offset.zero;
                _profileImageScale = 1.0;
              });
            },
          ),
        ],
      ),
      body: Container(
        decoration: isPremium
            ? BoxDecoration(gradient: AppTheme.premiumGoldBlackGradient)
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoadingUser
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // Template preview
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _buildTemplatePreview(isPremium),

                          // Banner selection horizontal list
                          _buildBannerSelectionList(isPremium),

                          const SizedBox(height: 16),

                          // Image source selection
                          _buildImageSourceSelection(isPremium),

                          const SizedBox(height: 16),

                          // Image shape selection
                          _buildImageShapeSelection(isPremium),

                          const SizedBox(height: 16),

                          // Add Image button
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Container(
                              width: double.infinity,
                              height: 50,
                              decoration: BoxDecoration(
                                gradient: isPremium 
                                    ? LinearGradient(
                                        colors: [AppTheme.premiumGold, AppTheme.premiumGold.withAlpha(200)],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      )
                                    : LinearGradient(
                                        colors: [AppTheme.primaryBlue, AppTheme.primaryBlue.withAlpha(200)],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue).withAlpha(100),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: _showAddImageBottomSheet,
                                  borderRadius: BorderRadius.circular(12),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.add_photo_alternate,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                        const SizedBox(width: 12),
                                        const Text(
                                          'Add Additional Image',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // Additional Images List (if any)
                          if (_additionalImages.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              child: Text(
                                'Additional Images (${_additionalImages.length})',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 80,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: _additionalImages.length,
                                itemBuilder: (context, index) {
                                  final image = _additionalImages[index];
                                  return _buildAdditionalImageItem(image, index, isPremium);
                                },
                              ),
                            ),
                          ],

                          const SizedBox(height: 32), // Increased space between buttons

                          // Done button
                          _buildDoneButton(isPremium),

                          const SizedBox(height: 16),
                        ],
                      ),
                    ),
                  ),

                  // Action buttons
                  ActionButtons.buildSharingButtonsRow(
                    context: context,
                    isPremium: isPremium,
                    onDownload: () => SharingUtilities.downloadTemplate(context, _screenshotController),
                    onShareWhatsApp: () => SharingUtilities.shareToWhatsApp(context, _screenshotController),
                    onShare: () => SharingUtilities.shareTemplate(context, _screenshotController),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildBannerSelectionList(bool isPremium) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Select Banner',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
          ),
        ),

        // Horizontal banner list
        SizedBox(
          height: 100,
          child: _isLoadingBanners
              ? const Center(child: CircularProgressIndicator())
              : ListView.builder(
                  controller: _bannerScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _banners.length + 1 + (_isLoadingMoreBanners ? 1 : 0), // +1 for "No Banner", +1 for loading
                  itemBuilder: (context, index) {
                    // "No Banner" option
                    if (index == 0) {
                      return _buildBannerItem(
                        null,
                        'No Banner',
                        isPremium,
                        isSelected: _selectedBanner == null,
                      );
                    }

                    // Regular banners
                    final bannerIndex = index - 1;
                    if (bannerIndex >= 0 && bannerIndex < _banners.length) {
                      final banner = _banners[bannerIndex];
                      return _buildBannerItem(
                        banner,
                        banner.title ?? 'Banner',
                        isPremium,
                        isSelected: _selectedBanner?.imageUrl == banner.imageUrl,
                      );
                    }

                    // Loading indicator
                    if (_isLoadingMoreBanners && bannerIndex == _banners.length) {
                      return Container(
                        width: 80,
                        margin: const EdgeInsets.only(right: 8),
                        child: const Center(child: CircularProgressIndicator()),
                      );
                    }

                    return const SizedBox.shrink();
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildBannerItem(BannerItem? banner, String title, bool isPremium, {required bool isSelected}) {
    return GestureDetector(
      onTap: () => _selectBanner(banner),
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                : Colors.grey.withAlpha(100),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          children: [
            // Banner image or placeholder
            Expanded(
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                child: banner != null
                    ? CachedNetworkImage(
                        imageUrl: banner.imageUrl,
                        width: double.infinity,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[200],
                          child: const Icon(Icons.error, size: 20),
                        ),
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: const Icon(Icons.clear, size: 30),
                      ),
              ),
            ),

            // Title
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
              decoration: BoxDecoration(
                color: isSelected
                    ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                    : Colors.grey[100],
                borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
              ),
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : Colors.black87,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Additional Images Methods
  void _showAddImageBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAddImageBottomSheet(),
    );
  }

  Widget _buildAddImageBottomSheet() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return Container(
      decoration: BoxDecoration(
        color: isPremium ? AppTheme.premiumDarkGrey : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Text(
            'Add Image',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
            ),
          ),
          const SizedBox(height: 20),

          // Add Image from Gallery option
          ListTile(
            leading: Icon(
              Icons.photo_library,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
            title: Text(
              'Add Image from Gallery',
              style: TextStyle(
                color: isPremium ? Colors.white : AppTheme.textRichBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              'Select an image from your device',
              style: TextStyle(
                color: isPremium ? Colors.white70 : Colors.grey[600],
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              _addImageFromGallery();
            },
          ),

          const SizedBox(height: 10),

          // Select Image Source option
          ListTile(
            leading: Icon(
              Icons.image_search,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
            title: Text(
              'Select Image Source',
              style: TextStyle(
                color: isPremium ? Colors.white : AppTheme.textRichBlack,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              'Choose from profile, business, or political images',
              style: TextStyle(
                color: isPremium ? Colors.white70 : Colors.grey[600],
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              _showImageSourceSelection();
            },
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Future<void> _addImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _additionalImages.add({
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'source': 'gallery',
            'path': image.path,
            'offset': Offset.zero,
            'scale': 1.0,
            'shape': 'none',
          });
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Image added successfully!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error adding image: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _showImageSourceSelection() {
    showDialog(
      context: context,
      builder: (context) => _buildImageSourceDialog(),
    );
  }

  Widget _buildImageSourceDialog() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isPremium = themeProvider.isPremium;

    return AlertDialog(
      backgroundColor: isPremium ? AppTheme.premiumDarkGrey : Colors.white,
      title: Text(
        'Select Image Source',
        style: TextStyle(
          color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Profile photo option
          ListTile(
            leading: Icon(
              Icons.person,
              color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
            ),
            title: Text(
              'Profile Photo',
              style: TextStyle(
                color: isPremium ? Colors.white : AppTheme.textRichBlack,
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              _addImageFromSource('profile');
            },
          ),

          // Business logo option (if business user)
          if (_user?.userType == 'businessman')
            ListTile(
              leading: Icon(
                Icons.business,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
              title: Text(
                'Business Logo',
                style: TextStyle(
                  color: isPremium ? Colors.white : AppTheme.textRichBlack,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _addImageFromSource('business');
              },
            ),

          // Political photo option (if political user)
          if (_user?.userType == 'politician')
            ListTile(
              leading: Icon(
                Icons.account_balance,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
              title: Text(
                'Political Photo',
                style: TextStyle(
                  color: isPremium ? Colors.white : AppTheme.textRichBlack,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _addImageFromSource('political');
              },
            ),
        ],
      ),
    );
  }

  void _addImageFromSource(String source) {
    String? imageUrl;

    switch (source) {
      case 'profile':
        imageUrl = _user?.photoUrl;
        break;
      case 'business':
        if (_user?.userType == 'businessman' && _user?.businessProfile != null) {
          imageUrl = _user!.businessProfile!.logoUrl;
          if (imageUrl == null || imageUrl.isEmpty) {
            // Check parameter values for logo
            if (_user!.businessProfile!.parameterValues != null) {
              for (var entry in _user!.businessProfile!.parameterValues!.entries) {
                if (entry.key.toLowerCase().contains('logo') &&
                    entry.value is String &&
                    (entry.value as String).isNotEmpty) {
                  imageUrl = entry.value as String;
                  break;
                }
              }
            }
          }
        }
        break;
      case 'political':
        if (_user?.userType == 'politician' && _user?.politicalProfile != null) {
          final paramValues = _user!.politicalProfile!.parameterValues;
          if (paramValues != null) {
            imageUrl = paramValues['political_photo'] as String?;
          }
        }
        break;
    }

    if (imageUrl != null && imageUrl.isNotEmpty) {
      setState(() {
        _additionalImages.add({
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'source': source,
          'url': imageUrl,
          'offset': Offset.zero,
          'scale': 1.0,
          'shape': 'none',
        });
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${source[0].toUpperCase()}${source.substring(1)} image added successfully!'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('No ${source} image available'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _removeAdditionalImage(String imageId) {
    setState(() {
      _additionalImages.removeWhere((image) => image['id'] == imageId);
    });
  }

  Widget _buildAdditionalImageItem(Map<String, dynamic> image, int index, bool isPremium) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // Image with tap to select shape
          Expanded(
            child: GestureDetector(
              onTap: () => _showShapeSelectionForImage(index),
              child: Stack(
                children: [
                  // Image
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: image['source'] == 'gallery'
                          ? Image.file(
                              File(image['path']),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.error, size: 20),
                                );
                              },
                            )
                          : CachedNetworkImage(
                              imageUrl: image['url'],
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey[200],
                                child: const Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey[200],
                                child: const Icon(Icons.error, size: 20),
                              ),
                            ),
                    ),
                  ),

                  // Shape indicator
                  if (image['shape'] != 'none')
                    Positioned(
                      bottom: 2,
                      left: 2,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(150),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Icon(
                          _getShapeIcon(image['shape']),
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),

                  // Remove button
                  Positioned(
                    top: 2,
                    right: 2,
                    child: GestureDetector(
                      onTap: () => _removeAdditionalImage(image['id']),
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 14,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Shape name
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
            decoration: BoxDecoration(
              color: isPremium ? AppTheme.premiumGold.withAlpha(50) : AppTheme.primaryBlue.withAlpha(50),
              borderRadius: const BorderRadius.vertical(bottom: Radius.circular(6)),
            ),
            child: Text(
              _getShapeName(image['shape']),
              style: TextStyle(
                fontSize: 8,
                fontWeight: FontWeight.bold,
                color: isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getShapeIcon(String shape) {
    switch (shape) {
      case 'circle':
        return Icons.circle_outlined;
      case 'rectangle':
        return Icons.rectangle_outlined;
      case 'rounded_rectangle':
        return Icons.rounded_corner;
      case 'diamond':
        return Icons.diamond_outlined;
      case 'hexagon':
        return Icons.hexagon_outlined;
      default:
        return Icons.crop_original;
    }
  }

  String _getShapeName(String shape) {
    switch (shape) {
      case 'circle':
        return 'Circle';
      case 'rectangle':
        return 'Rectangle';
      case 'rounded_rectangle':
        return 'Rounded';
      case 'diamond':
        return 'Diamond';
      case 'hexagon':
        return 'Hexagon';
      default:
        return 'No Shape';
    }
  }

  void _showShapeSelectionForImage(int imageIndex) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    final isPremium = themeProvider.isPremium;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isPremium ? AppTheme.premiumDarkGrey : Colors.white,
        title: Text(
          'Select Shape',
          style: TextStyle(
            color: isPremium ? AppTheme.premiumGold : AppTheme.textRichBlack,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: GridView.builder(
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: _getShapeOptions().length,
            itemBuilder: (context, index) {
              final shape = _getShapeOptions()[index];
              final isSelected = _additionalImages[imageIndex]['shape'] == shape['id'];
              
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _additionalImages[imageIndex]['shape'] = shape['id'];
                  });
                  Navigator.pop(context);
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                          : Colors.grey.withAlpha(100),
                      width: isSelected ? 3 : 1,
                    ),
                    color: isSelected
                        ? (isPremium ? AppTheme.premiumGold.withAlpha(50) : AppTheme.primaryBlue.withAlpha(50))
                        : null,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        shape['icon'],
                        size: 24,
                        color: isSelected
                            ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                            : Colors.grey[600],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        shape['name'],
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected
                              ? (isPremium ? AppTheme.premiumGold : AppTheme.primaryBlue)
                              : (isPremium ? Colors.white : Colors.grey[600]),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Offset _constrainAdditionalImageOffset(Offset offset, double baseX, double baseY, double templateWidth, double templateHeight) {
    final leftBoundary = 0.0;
    final rightBoundary = templateWidth - 80.0;
    final topBoundary = 0.0;
    final bottomBoundary = templateHeight - 80.0;

    final constrainedX = offset.dx.clamp(leftBoundary, rightBoundary);
    final constrainedY = offset.dy.clamp(topBoundary, bottomBoundary);

    return Offset(
      constrainedX - baseX,
      constrainedY - baseY,
    );
  }
}
