import 'package:flutter/material.dart';

import '../../../../../core/theme/app_theme.dart';

/// A class that provides widgets for action buttons
class ActionButtons {
  /// Builds an action button with icon and label
  static Widget buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isPremium = false,
  }) {
    final Color iconColor = isPremium ? AppTheme.premiumGold : Colors.blue;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 24,
              color: iconColor,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isPremium ? Colors.white : Colors.black54,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a row of sharing buttons
  static Widget buildSharingButtonsRow({
    required BuildContext context,
    required VoidCallback onDownload,
    required VoidCallback onShareWhatsApp,
    required VoidCallback onShare,
    required bool isPremium,
    VoidCallback? onEdit,
  }) {
    return Container(
      margin: const EdgeInsets.fromLTRB(8.0, 0, 8.0, 8.0),
      decoration: BoxDecoration(
        color: isPremium ? Colors.black.withAlpha(20) : Colors.black.withAlpha(50),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withAlpha(30),
          width: 0.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Edit button (if onEdit callback is provided)
            if (onEdit != null) ...[
              buildActionButton(
                icon: Icons.edit_rounded,
                label: 'Edit',
                onTap: onEdit,
                isPremium: isPremium,
              ),
              // Vertical divider
              Container(
                height: 24,
                width: 1,
                color: Colors.grey.withAlpha(75), // ~0.3 opacity
              ),
            ],

            // Save to Gallery button
            buildActionButton(
              icon: Icons.save_alt_rounded,
              label: 'Save to Gallery',
              onTap: onDownload,
              isPremium: isPremium,
            ),

            // Vertical divider
            Container(
              height: 24,
              width: 1,
              color: Colors.grey.withAlpha(75), // ~0.3 opacity
            ),

            // WhatsApp button
            buildActionButton(
              icon: Icons.message_rounded,
              label: 'WhatsApp',
              onTap: onShareWhatsApp,
              isPremium: isPremium,
            ),

            // Vertical divider
            Container(
              height: 24,
              width: 1,
              color: Colors.grey.withAlpha(75), // ~0.3 opacity
            ),

            // Share button
            buildActionButton(
              icon: Icons.share_rounded,
              label: 'Share',
              onTap: onShare,
              isPremium: isPremium,
            ),
          ],
        ),
      ),
    );
  }
}
