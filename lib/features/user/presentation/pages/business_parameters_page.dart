import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/theme_provider.dart';
import '../../domain/entities/business_profile.dart';
import '../../domain/usecases/user_service.dart';
import '../widgets/dynamic_business_parameter_form.dart';

class BusinessParametersPage extends StatefulWidget {
  final BusinessProfile? initialProfile;
  
  const BusinessParametersPage({
    super.key,
    this.initialProfile,
  });

  @override
  State<BusinessParametersPage> createState() => _BusinessParametersPageState();
}

class _BusinessParametersPageState extends State<BusinessParametersPage> {
  BusinessProfile? _businessProfile;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _businessProfile = widget.initialProfile;
    if (_businessProfile == null) {
      _loadBusinessProfile();
    }
  }

  Future<void> _loadBusinessProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userService = context.read<UserService>();
      final profile = await userService.getBusinessProfile();
      
      if (mounted) {
        setState(() {
          _businessProfile = profile;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load business profile: $e')),
        );
      }
    }
  }

  Future<void> _refreshProfile() async {
    await _loadBusinessProfile();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Scaffold(
      appBar: themeProvider.gradientAppBar(
        title: 'Business Parameters',
      ),
      body: Container(
        decoration: themeProvider.isPremium
            ? BoxDecoration(
                gradient: AppTheme.premiumGoldBlackGradient,
              )
            : BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppTheme.backgroundWhite,
                    AppTheme.lightGradientBg,
                  ],
                ),
              ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: DynamicBusinessParameterForm(
                  initialProfile: _businessProfile,
                  onProfileUpdated: _refreshProfile,
                ),
              ),
      ),
    );
  }
}
