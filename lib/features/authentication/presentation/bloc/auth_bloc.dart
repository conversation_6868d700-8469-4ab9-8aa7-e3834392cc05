import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/utils/logger.dart';
import '../../../../features/user/domain/usecases/user_service.dart';
import '../../data/repositories/auth_repository.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  final UserService _userService;

  AuthBloc({
    required AuthRepository authRepository,
    required UserService userService,
  })  : _authRepository = authRepository,
        _userService = userService,
        super(AuthInitial()) {
    on<SendOtpEvent>(_onSendOtp);
    on<VerifyOtpEvent>(_onVerifyOtp);
    on<SignOutEvent>(_onSignOut);
    on<CheckAuthStatusEvent>(_onCheckAuthStatus);
    on<NoOpAuthEvent>(_onNoOpAuth);
  }

  Future<void> _onSendOtp(
    SendOtpEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (emit.isDone) return;

    emit(AuthLoading());

    // Create completer to handle the async callbacks properly
    final completer = Completer<void>();

    try {
      // Store the callbacks that will be used to emit states
      PhoneAuthCredential? storedCredential;
      FirebaseAuthException? authException;
      String? storedVerificationId;
      int? storedResendToken;

      _authRepository.sendOTP(
        phoneNumber: event.phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) {
          // Store the credential for later use
          storedCredential = credential;
          if (!completer.isCompleted) {
            completer.complete();
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          // Store the exception for later use
          authException = e;
          AppLogger.error('Firebase Auth Exception: ${e.code} - ${e.message}');
          if (!completer.isCompleted) {
            completer.complete();
          }
        },
        codeSent: (String verificationId, int? resendToken) {
          // Store the verification ID and resend token for later use
          storedVerificationId = verificationId;
          storedResendToken = resendToken;
          if (!completer.isCompleted) {
            completer.complete();
          }
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
          // We don't need to do anything here for now
        },
      );

      // Wait for one of the callbacks to complete
      await completer.future;

      if (emit.isDone) return;

      // Now handle the stored values
      if (authException != null) {
        String errorMessage = 'Verification failed';

        // Provide more specific error messages based on the error code
        switch (authException!.code) {
          case 'invalid-phone-number':
            errorMessage = 'The phone number format is incorrect. Please enter a valid phone number.';
            break;
          case 'quota-exceeded':
            errorMessage = 'SMS quota exceeded. Please try again later.';
            break;
          case 'user-disabled':
            errorMessage = 'This user has been disabled. Please contact support.';
            break;
          case 'operation-not-allowed':
            errorMessage = 'Phone authentication is not enabled. Please contact support.';
            break;
          case 'captcha-check-failed':
            errorMessage = 'reCAPTCHA verification failed. Please try again.';
            break;
          case 'missing-client-identifier':
            errorMessage = 'The app verification process failed. Please try again or contact support.';
            break;
          case 'too-many-requests':
            errorMessage = 'Too many requests from this device. We have temporarily blocked all requests from this device due to unusual activity. Please try again after some time (usually a few hours).';
            break;
          default:
            errorMessage = authException!.message ?? 'Verification failed. Please try again.';
        }

        emit(AuthFailure(message: errorMessage));
      } else if (storedCredential != null) {
        try {
          // Use the credential directly with Firebase Auth
          final userCredential = await FirebaseAuth.instance.signInWithCredential(storedCredential!);

          if (emit.isDone) return;

          if (userCredential.user != null) {
            emit(AuthSuccess(user: userCredential.user!));
          } else {
            emit(const AuthFailure(message: 'Failed to get user data'));
          }
        } catch (e) {
          if (emit.isDone) return;
          emit(AuthFailure(message: e.toString()));
        }
      } else if (storedVerificationId != null) {
        emit(OtpSent(
          verificationId: storedVerificationId!,
          resendToken: storedResendToken,
        ));
      }
    } catch (e) {
      if (emit.isDone) return;
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onVerifyOtp(
    VerifyOtpEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (emit.isDone) return;

    emit(AuthLoading());
    try {
      // Add a delay to ensure Firebase Auth has time to process
      await Future.delayed(const Duration(milliseconds: 500));

      // Verify OTP
      await _authRepository.verifyOTP(
        verificationId: event.verificationId,
        smsCode: event.otp,
      );

      if (emit.isDone) return;

      // Get the current user from Firebase Auth
      final currentUser = _authRepository.getCurrentUser();

      if (currentUser != null) {
        bool isNewUser = false;

        try {
          // Check if the user already exists in Firestore
          final userExists = await _userService.userExists(currentUser.uid);

          if (!userExists) {
            // This is a new user
            isNewUser = true;

            // Create a new user in Firestore with minimal info
            await _userService.createUser(
              uid: currentUser.uid,
              name: currentUser.displayName ?? 'User',
              email: currentUser.email,
              phoneNumber: currentUser.phoneNumber,
              photoUrl: currentUser.photoURL,
            );
            AppLogger.info('Created new user in Firestore: ${currentUser.uid}');
          } else {
            // Check if the user has completed their profile
            final userModel = await _userService.getUserById(currentUser.uid);
            if (userModel != null && !userModel.isProfileComplete) {
              isNewUser = true;
              AppLogger.info('User exists but profile is incomplete: ${currentUser.uid}');
            } else {
              AppLogger.info('Existing user with complete profile: ${currentUser.uid}');
            }
          }
        } catch (e) {
          // Log the error but don't fail the authentication
          AppLogger.error('Failed to check user in Firestore', e);
        }

        emit(AuthSuccess(user: currentUser, isNewUser: isNewUser));
      } else {
        emit(const AuthFailure(message: 'Failed to get user data'));
      }
    } on FirebaseAuthException catch (e) {
      if (emit.isDone) return;
      emit(AuthFailure(message: e.message ?? 'Invalid OTP'));
    } catch (e) {
      if (emit.isDone) return;
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onSignOut(
    SignOutEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (emit.isDone) return;

    emit(AuthLoading());
    try {
      await _authRepository.signOut();

      if (emit.isDone) return;
      emit(SignedOut());
    } catch (e) {
      if (emit.isDone) return;
      emit(AuthFailure(message: e.toString()));
    }
  }

  Future<void> _onCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (emit.isDone) return;

    emit(AuthLoading());
    try {
      final isSignedIn = _authRepository.isUserSignedIn();

      if (emit.isDone) return;

      if (isSignedIn) {
        final user = _authRepository.getCurrentUser();
        if (user != null) {
          try {
            // Check if the user has completed their profile
            final userModel = await _userService.getUserById(user.uid);
            final isNewUser = userModel == null || !userModel.isProfileComplete;

            emit(AuthSuccess(user: user, isNewUser: isNewUser));
          } catch (e) {
            AppLogger.error('Failed to check user profile status', e);
            emit(AuthSuccess(user: user, isNewUser: false));
          }
        } else {
          emit(SignedOut());
        }
      } else {
        emit(SignedOut());
      }
    } catch (e) {
      if (emit.isDone) return;
      emit(AuthFailure(message: e.toString()));
    }
  }

  // Handler for NoOpAuthEvent - does nothing but maintains the current state
  void _onNoOpAuth(NoOpAuthEvent event, Emitter<AuthState> emit) {
    // This is intentionally empty as we don't want to change the state
    // It's used when rebuilding the AuthBloc provider without changing the state
  }
}
