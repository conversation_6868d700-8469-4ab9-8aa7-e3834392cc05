import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pinput/pinput.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/services/notification_service.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../../features/user/presentation/pages/profile_completion_page.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';

class OtpVerificationPage extends StatefulWidget {
  final String verificationId;
  final String phoneNumber;

  const OtpVerificationPage({
    super.key,
    required this.verificationId,
    required this.phoneNumber,
  });

  @override
  State<OtpVerificationPage> createState() => _OtpVerificationPageState();
}

class _OtpVerificationPageState extends State<OtpVerificationPage> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  bool _isLoading = false;

  // Countdown timer variables
  Timer? _timer;
  int _countdownSeconds = 120; // 2 minutes
  bool _canResend = false;

  @override
  void initState() {
    super.initState();
    _startCountdown();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _otpController.dispose();
    super.dispose();
  }

  void _startCountdown() {
    setState(() {
      _countdownSeconds = 120;
      _canResend = false;
    });

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdownSeconds > 0) {
        setState(() {
          _countdownSeconds--;
        });
      } else {
        setState(() {
          _canResend = true;
        });
        timer.cancel();
      }
    });
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppTheme.primaryBlue.withValues(alpha: 0.05),
              Colors.white,
              AppTheme.accentViolet.withValues(alpha: 0.03),
            ],
          ),
        ),
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthLoading) {
              setState(() {
                _isLoading = true;
              });
            } else {
              setState(() {
                _isLoading = false;
              });
            }

            if (state is AuthSuccess) {
              // Log analytics events
              if (state.isNewUser) {
                AnalyticsService().logSignUp('phone');
              } else {
                AnalyticsService().logLoginSuccess('phone');
              }

              // Initialize user-specific notification subscriptions
              NotificationService().initializeUserSubscriptions();

              if (state.isNewUser) {
                // Navigate to profile completion page
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(
                    builder: (context) => const ProfileCompletionPage(),
                  ),
                  (route) => false, // Remove all previous routes
                );
              } else {
                // Navigate to home page and remove all previous routes
                Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
              }
            } else if (state is AuthFailure) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.message,
                    style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                  ),
                  backgroundColor: AppTheme.errorRed,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            return SafeArea(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 30),

                        // Modern Header with Back Button
                        Row(
                          children: [
                            // Back Button
                            // Container(
                            //   width: 44,
                            //   height: 44,
                            //   decoration: BoxDecoration(
                            //     color: Colors.white,
                            //     borderRadius: BorderRadius.circular(14),
                            //     boxShadow: [
                            //       BoxShadow(
                            //         color: Colors.black.withValues(alpha: 0.06),
                            //         blurRadius: 10,
                            //         offset: const Offset(0, 2),
                            //       ),
                            //     ],
                            //   ),
                            //   child: Material(
                            //     color: Colors.transparent,
                            //     child: InkWell(
                            //       borderRadius: BorderRadius.circular(14),
                            //       onTap: () => Navigator.pop(context),
                            //       child: Icon(
                            //         Icons.arrow_back_ios_new_rounded,
                            //         color: AppTheme.textRichBlack,
                            //         size: 20,
                            //       ),
                            //     ),
                            //   ),
                            // ),
                            // const Spacer(),
                            // Title
                            Text(
                              'Verification',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: AppTheme.textRichBlack,
                              ),
                            ),
                            const Spacer(),
                            const SizedBox(width: 44), // Balance the row
                          ],
                        ),

                              const SizedBox(height: 40),

                              // Modern Icon and Header Section
                              Column(
                                children: [
                                  // Modern OTP Icon
                                  Container(
                                    height: 80,
                                    width: 80,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          AppTheme.primaryBlue,
                                          AppTheme.accentViolet,
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppTheme.primaryBlue.withValues(alpha: 0.3),
                                          blurRadius: 15,
                                          offset: const Offset(0, 6),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.security_rounded,
                                      size: 40,
                                      color: Colors.white,
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Title and Description
                                  Text(
                                    'Enter Verification Code',
                                    style: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w800,
                                      color: AppTheme.textRichBlack,
                                      letterSpacing: -0.5,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),

                                  const SizedBox(height: 8),

                                  RichText(
                                    textAlign: TextAlign.center,
                                    text: TextSpan(
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: AppTheme.secondaryText,
                                        fontWeight: FontWeight.w400,
                                        height: 1.4,
                                      ),
                                      children: [
                                        const TextSpan(text: 'We sent a 6-digit code to\n'),
                                        TextSpan(
                                          text: widget.phoneNumber,
                                          style: TextStyle(
                                            color: AppTheme.primaryBlue,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 40),

                              // Modern OTP Input
                              Container(
                                margin: const EdgeInsets.symmetric(horizontal: 4),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.04),
                                      blurRadius: 15,
                                      offset: const Offset(0, 3),
                                    ),
                                    BoxShadow(
                                      color: AppTheme.primaryBlue.withValues(alpha: 0.06),
                                      blurRadius: 30,
                                      offset: const Offset(0, 6),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(24.0),
                            child: Column(
                              children: [
                                // Modern Pinput
                                Pinput(
                                  length: 6,
                                  controller: _otpController,
                                  onChanged: (value) {
                                    // Value is automatically handled by controller
                                  },
                                  onCompleted: (value) {
                                    // Value is automatically handled by controller
                                  },
                                  defaultPinTheme: PinTheme(
                                    width: 45,
                                    height: 55,
                                    textStyle: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      color: AppTheme.textRichBlack,
                                    ),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF8F9FA),
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color: const Color(0xFFE9ECEF),
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                  focusedPinTheme: PinTheme(
                                    width: 45,
                                    height: 55,
                                    textStyle: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      color: AppTheme.textRichBlack,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color: AppTheme.primaryBlue,
                                        width: 2,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppTheme.primaryBlue.withValues(alpha: 0.2),
                                          blurRadius: 12,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                  ),
                                  submittedPinTheme: PinTheme(
                                    width: 45,
                                    height: 55,
                                    textStyle: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.white,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                        colors: [
                                          AppTheme.primaryBlue,
                                          AppTheme.accentViolet,
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: AppTheme.primaryBlue.withValues(alpha: 0.4),
                                          blurRadius: 12,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                    ),
                                  ),
                                  errorPinTheme: PinTheme(
                                    width: 45,
                                    height: 55,
                                    textStyle: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.white,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppTheme.errorRed,
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color: AppTheme.errorRed,
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Timer and Resend Section
                                Text(
                                  _canResend
                                    ? 'Code expired'
                                    : 'Code expires in ${_formatTime(_countdownSeconds)}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: _canResend ? AppTheme.errorRed : AppTheme.secondaryText,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 30),

                        // Modern Verify Button
                        Container(
                          width: double.infinity,
                          height: 56,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppTheme.primaryBlue,
                                AppTheme.accentViolet,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: AppTheme.primaryBlue.withValues(alpha: 0.4),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(20),
                              onTap: _isLoading ? null : () {
                                // Manual validation to prevent layout shifts
                                final otpText = _otpController.text.trim();

                                if (otpText.isEmpty) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Please enter the OTP code',
                                        style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                                      ),
                                      backgroundColor: AppTheme.errorRed,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  );
                                  return;
                                }

                                if (otpText.length != 6) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Please enter the complete 6-digit OTP',
                                        style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                                      ),
                                      backgroundColor: AppTheme.errorRed,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  );
                                  return;
                                }

                                // Proceed with OTP verification
                                context.read<AuthBloc>().add(
                                      VerifyOtpEvent(
                                        otp: otpText,
                                        verificationId: widget.verificationId,
                                      ),
                                    );
                              },
                              child: Center(
                                child: _isLoading
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2.5,
                                      ),
                                    )
                                  : Text(
                                      'Verify & Continue',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.white,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                              ),
                            ),
                          ),
                        ),

                              const SizedBox(height: 24),

                              // Modern Resend Section with Timer
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Didn\'t receive the code? ',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppTheme.secondaryText,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  if (_canResend)
                                    GestureDetector(
                                      onTap: _isLoading ? null : () {
                                        // Resend OTP
                                        context.read<AuthBloc>().add(
                                              SendOtpEvent(
                                                phoneNumber: widget.phoneNumber,
                                              ),
                                            );

                                        // Restart countdown
                                        _startCountdown();

                                        // Show a snackbar to inform the user
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Resending OTP to ${widget.phoneNumber}...',
                                              style: AppTheme.bodyMedium.copyWith(color: Colors.white),
                                            ),
                                            duration: const Duration(seconds: 2),
                                            backgroundColor: AppTheme.primaryBlue,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(12),
                                            ),
                                          ),
                                        );
                                      },
                                      child: Text(
                                        'Resend',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: AppTheme.primaryBlue,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    )
                                  else
                                    Text(
                                      'Resend in ${_formatTime(_countdownSeconds)}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: AppTheme.secondaryText,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                ],
                              ),

                              const SizedBox(height: 30),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
