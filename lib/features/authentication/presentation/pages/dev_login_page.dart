import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';
import 'phone_auth_page.dart';

/// A temporary development login page that bypasses phone authentication
/// This is only for development purposes while the device is blocked by Firebase
class DevLoginPage extends StatelessWidget {
  const DevLoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppTheme.gradientAppBar(
        title: 'Development Login',
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Development Login',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 10),
            const Text(
              'This is a temporary login for development purposes only.\n'
              'Use this while the device is blocked by Firebase.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                // Navigate directly to the home page
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const DevelopmentHomePage(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
              child: const Text(
                'Login as Developer',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
            const SizedBox(height: 20),
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const PhoneAuthPage(),
                  ),
                );
              },
              child: const Text('Try Phone Authentication'),
            ),
          ],
        ),
      ),
    );
  }
}

/// A temporary home page for development
class DevelopmentHomePage extends StatelessWidget {
  const DevelopmentHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppTheme.gradientAppBar(
        title: 'QuickPosters (Dev Mode)',
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              // Go back to the login page
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const DevLoginPage(),
                ),
              );
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Development Mode',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'You are logged in as a developer.\n'
              'This is a temporary mode while Firebase phone authentication is blocked.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
